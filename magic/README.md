# 智能投放系统 - 精美首页

基于 React Bits 组件库打造的现代化保险投放系统首页，具有精美的动画效果和交互体验。

## 🎨 特性

- ✨ **现代化设计**: 基于 React Bits 组件库的精美界面
- 🎭 **丰富动画**: Framer Motion 驱动的流畅动画效果
- 📱 **响应式设计**: 完美适配桌面端和移动端
- 🎯 **专业内容**: 保险行业专业文案和功能展示
- ⚡ **高性能**: Next.js 14 + TypeScript 构建

## 🚀 快速开始

### 安装依赖

```bash
cd magic
npm install
# 或
yarn install
# 或
pnpm install
```

### 启动开发服务器

```bash
npm run dev
# 或
yarn dev
# 或
pnpm dev
```

打开 [http://localhost:3000](http://localhost:3000) 查看效果。

## 📦 项目结构

```
magic/
├── app/                    # Next.js 13+ App Router
│   ├── globals.css        # 全局样式
│   ├── layout.tsx         # 根布局
│   └── page.tsx           # 首页
├── components/            # React 组件
│   ├── hero-section.tsx   # 英雄区域
│   ├── magic-bento.tsx    # Magic Bento 网格
│   └── process-timeline.tsx # 流程时间线
├── package.json
├── tailwind.config.js     # Tailwind CSS 配置
└── tsconfig.json          # TypeScript 配置
```

## 🎯 页面组件

### 1. Hero Section (英雄区域)
- 动态粒子背景
- 渐变文字效果
- 动画按钮
- 关键数据展示

### 2. Magic Bento Grid (功能展示)
- 响应式网格布局
- 悬停动画效果
- 发光边框
- 功能卡片展示

### 3. Process Timeline (流程时间线)
- 垂直时间线布局
- 步骤动画
- 交互式悬停效果
- 流程数据展示

## 🛠 技术栈

- **框架**: Next.js 14
- **语言**: TypeScript
- **样式**: Tailwind CSS
- **动画**: Framer Motion
- **图标**: Lucide React

## 🎨 设计特色

- **深色主题**: 现代化的深色背景
- **渐变效果**: 多层次渐变背景
- **动画交互**: 丰富的鼠标悬停和滚动动画
- **响应式**: 完美适配各种屏幕尺寸

## 📝 内容说明

页面内容完全基于保险投放系统的业务场景：

- **核心功能**: 流量聚合、智能匹配、数据分析、实时推送等
- **业务流程**: 客户提交需求 → 智能匹配 → 推送通知 → 服务响应
- **关键数据**: 98.5%客户满意度、2分钟响应时间、40%+转化率提升

## 🚀 部署

### Vercel 部署

```bash
npm run build
```

推荐使用 [Vercel](https://vercel.com) 进行部署，支持自动部署和优化。

### 其他平台

项目支持部署到任何支持 Next.js 的平台，如 Netlify、Railway 等。

## 📄 许可证

本项目仅用于展示目的。React Bits 组件可能需要相应的许可证用于商业用途。
