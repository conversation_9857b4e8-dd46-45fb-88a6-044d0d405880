'use client'

import { motion } from 'framer-motion'
import { useState } from 'react'

interface BentoItemProps {
  title: string
  description: string
  icon: string
  className?: string
  children?: React.ReactNode
}

const BentoItem = ({ title, description, icon, className = '', children }: BentoItemProps) => {
  const [isHovered, setIsHovered] = useState(false)

  return (
    <motion.div
      className={`relative overflow-hidden rounded-3xl bg-gradient-to-br from-white/10 to-white/5 backdrop-blur-sm border border-white/20 p-6 ${className}`}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
      whileHover={{ scale: 1.02, y: -5 }}
      transition={{ type: "spring", stiffness: 300, damping: 30 }}
    >
      {/* Animated border */}
      <motion.div
        className="absolute inset-0 rounded-3xl"
        style={{
          background: isHovered 
            ? 'linear-gradient(45deg, #3b82f6, #8b5cf6, #06b6d4, #3b82f6)'
            : 'transparent',
          backgroundSize: '300% 300%',
        }}
        animate={{
          backgroundPosition: isHovered ? ['0% 50%', '100% 50%', '0% 50%'] : '0% 50%',
        }}
        transition={{ duration: 3, repeat: Infinity, ease: "linear" }}
      />
      
      {/* Content */}
      <div className="relative z-10">
        <div className="text-4xl mb-4">{icon}</div>
        <h3 className="text-xl font-bold text-white mb-2">{title}</h3>
        <p className="text-gray-300 text-sm leading-relaxed">{description}</p>
        {children}
      </div>

      {/* Glow effect */}
      <motion.div
        className="absolute inset-0 rounded-3xl opacity-0"
        style={{
          background: 'radial-gradient(circle at center, rgba(59, 130, 246, 0.3) 0%, transparent 70%)',
        }}
        animate={{ opacity: isHovered ? 1 : 0 }}
        transition={{ duration: 0.3 }}
      />
    </motion.div>
  )
}

export const MagicBento = () => {
  return (
    <div className="max-w-7xl mx-auto px-4 py-16">
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.8 }}
        className="text-center mb-16"
      >
        <h2 className="text-4xl md:text-6xl font-bold text-white mb-6">
          智能投放系统
          <span className="gradient-text block">核心功能</span>
        </h2>
        <p className="text-xl text-gray-300 max-w-3xl mx-auto">
          从客户需求到服务交付的完整流程，实现高效精准的保险服务匹配
        </p>
      </motion.div>

      <div className="grid grid-cols-1 md:grid-cols-4 lg:grid-cols-6 gap-6 auto-rows-[200px]">
        <BentoItem
          title="流量聚合"
          description="通过微信小程序聚合车险、财险等多种保险需求，为行业提供稳定优质的流量来源"
          icon="📱"
          className="md:col-span-2 lg:col-span-3 md:row-span-2"
        >
          <div className="mt-4 space-y-2">
            <div className="flex items-center text-sm text-gray-400">
              <span className="w-2 h-2 bg-green-400 rounded-full mr-2"></span>
              车险需求聚合
            </div>
            <div className="flex items-center text-sm text-gray-400">
              <span className="w-2 h-2 bg-blue-400 rounded-full mr-2"></span>
              财险流量整合
            </div>
          </div>
        </BentoItem>

        <BentoItem
          title="智能匹配"
          description="基于大数据算法实现客户需求与服务商的精准匹配"
          icon="🤖"
          className="md:col-span-2 lg:col-span-2"
        />

        <BentoItem
          title="数据分析"
          description="实时监控投放数据，提供详细的分析报告"
          icon="📊"
          className="md:col-span-2 lg:col-span-1 md:row-span-2"
        />

        <BentoItem
          title="实时推送"
          description="向匹配的服务商实时推送客户需求，5分钟内必须响应"
          icon="🔔"
          className="md:col-span-2 lg:col-span-2"
        />

        <BentoItem
          title="服务管理"
          description="为服务商提供完善的管理后台，支持订单管理、客户跟进"
          icon="⚙️"
          className="md:col-span-2 lg:col-span-2"
        />

        <BentoItem
          title="效果优化"
          description="基于投放数据持续优化算法模型，提升匹配精度"
          icon="📈"
          className="md:col-span-2 lg:col-span-3"
        />
      </div>
    </div>
  )
}
