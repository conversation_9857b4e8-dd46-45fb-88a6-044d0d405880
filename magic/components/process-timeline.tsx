'use client'

import { motion } from 'framer-motion'
import { useState } from 'react'

interface TimelineStepProps {
  step: number
  title: string
  description: string
  details: string[]
  icon: string
  isActive?: boolean
  delay: number
}

const TimelineStep = ({ step, title, description, details, icon, isActive = false, delay }: TimelineStepProps) => {
  const [isHovered, setIsHovered] = useState(false)

  return (
    <motion.div
      className="relative"
      initial={{ opacity: 0, x: -50 }}
      whileInView={{ opacity: 1, x: 0 }}
      transition={{ duration: 0.6, delay }}
      viewport={{ once: true }}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      <div className="flex items-start space-x-6">
        {/* Timeline line and icon */}
        <div className="flex flex-col items-center">
          <motion.div
            className={`relative w-16 h-16 rounded-full flex items-center justify-center text-2xl ${
              isActive || isHovered 
                ? 'bg-gradient-to-br from-blue-500 to-purple-600' 
                : 'bg-white/10 border-2 border-white/20'
            }`}
            whileHover={{ scale: 1.1 }}
            transition={{ type: "spring", stiffness: 300, damping: 30 }}
          >
            {/* Glow effect */}
            <motion.div
              className="absolute inset-0 rounded-full"
              style={{
                boxShadow: (isActive || isHovered) ? '0 0 30px rgba(59, 130, 246, 0.6)' : 'none',
              }}
              transition={{ duration: 0.3 }}
            />
            
            <span className="relative z-10">{icon}</span>
            
            {/* Step number */}
            <motion.div
              className="absolute -top-2 -right-2 w-6 h-6 bg-gradient-to-br from-purple-500 to-pink-500 rounded-full flex items-center justify-center text-xs font-bold text-white"
              initial={{ scale: 0 }}
              animate={{ scale: 1 }}
              transition={{ delay: delay + 0.3, type: "spring" }}
            >
              {step}
            </motion.div>
          </motion.div>
          
          {/* Connecting line */}
          {step < 4 && (
            <motion.div
              className="w-0.5 h-24 bg-gradient-to-b from-blue-500/50 to-purple-500/50 mt-4"
              initial={{ height: 0 }}
              whileInView={{ height: 96 }}
              transition={{ duration: 0.8, delay: delay + 0.2 }}
              viewport={{ once: true }}
            />
          )}
        </div>

        {/* Content */}
        <motion.div
          className={`flex-1 pb-12 ${isHovered ? 'transform translate-x-2' : ''}`}
          transition={{ duration: 0.3 }}
        >
          <motion.div
            className={`p-6 rounded-2xl backdrop-blur-sm border transition-all duration-300 ${
              isActive || isHovered
                ? 'bg-white/10 border-white/30 shadow-xl'
                : 'bg-white/5 border-white/10'
            }`}
          >
            <h3 className="text-2xl font-bold text-white mb-3">{title}</h3>
            <p className="text-gray-300 text-lg mb-4 leading-relaxed">{description}</p>
            
            <div className="space-y-2">
              {details.map((detail, index) => (
                <motion.div
                  key={index}
                  className="flex items-center text-gray-400"
                  initial={{ opacity: 0, x: -20 }}
                  whileInView={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.4, delay: delay + 0.4 + index * 0.1 }}
                  viewport={{ once: true }}
                >
                  <div className="w-2 h-2 bg-blue-400 rounded-full mr-3 flex-shrink-0" />
                  <span>{detail}</span>
                </motion.div>
              ))}
            </div>
          </motion.div>
        </motion.div>
      </div>
    </motion.div>
  )
}

export const ProcessTimeline = () => {
  const steps = [
    {
      step: 1,
      title: '客户提交需求',
      description: '用户通过微信小程序填写保险需求信息',
      details: [
        '选择保险类型（车险、财险等）',
        '填写基本信息和联系方式',
        '系统自动验证信息完整性'
      ],
      icon: '📱',
      isActive: true
    },
    {
      step: 2,
      title: '智能算法匹配',
      description: '系统自动分析客户需求，匹配最适合的服务商',
      details: [
        '基于地理位置就近匹配',
        '根据服务商专业度排序',
        '考虑历史成交率和评价'
      ],
      icon: '🤖'
    },
    {
      step: 3,
      title: '实时推送通知',
      description: '向匹配的服务商实时推送客户需求',
      details: [
        '多渠道通知（短信、微信、APP）',
        '5分钟内必须响应',
        '超时自动转给下一个服务商'
      ],
      icon: '🔔'
    },
    {
      step: 4,
      title: '服务商响应',
      description: '服务商主动联系客户，提供专业服务',
      details: [
        '平均响应时间：2分钟',
        '客户满意度：98.5%',
        '全程服务质量监控'
      ],
      icon: '📞'
    }
  ]

  return (
    <div className="max-w-4xl mx-auto px-4 py-20">
      <motion.div
        initial={{ opacity: 0, y: 30 }}
        whileInView={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.8 }}
        viewport={{ once: true }}
        className="text-center mb-16"
      >
        <h2 className="text-4xl md:text-6xl font-bold text-white mb-6">
          业务流程
          <span className="gradient-text block">智能化运作</span>
        </h2>
        <p className="text-xl text-gray-300 max-w-3xl mx-auto">
          从客户需求到服务交付的完整流程，每一步都经过精心设计和优化
        </p>
      </motion.div>

      <div className="space-y-0">
        {steps.map((step, index) => (
          <TimelineStep
            key={step.step}
            {...step}
            delay={index * 0.2}
          />
        ))}
      </div>

      {/* Success metrics */}
      <motion.div
        className="mt-20 p-8 rounded-3xl bg-gradient-to-br from-green-500/10 to-blue-500/10 backdrop-blur-sm border border-white/10"
        initial={{ opacity: 0, y: 30 }}
        whileInView={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.8, delay: 0.4 }}
        viewport={{ once: true }}
      >
        <div className="text-center">
          <h3 className="text-2xl font-bold text-white mb-6">流程效果</h3>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="text-center">
              <div className="text-3xl font-bold text-green-400 mb-2">40%+</div>
              <div className="text-gray-300">转化率提升</div>
            </div>
            <div className="text-center">
              <div className="text-3xl font-bold text-blue-400 mb-2">60%</div>
              <div className="text-gray-300">ROI增长</div>
            </div>
            <div className="text-center">
              <div className="text-3xl font-bold text-purple-400 mb-2">2分钟</div>
              <div className="text-gray-300">平均响应时间</div>
            </div>
          </div>
        </div>
      </motion.div>
    </div>
  )
}
