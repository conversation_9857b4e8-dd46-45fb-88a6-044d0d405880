import { HeroSection } from '@/components/hero-section'
import { MagicBento } from '@/components/magic-bento'
import { ProcessTimeline } from '@/components/process-timeline'

export default function Home() {
  return (
    <main className="relative">
      {/* Hero Section */}
      <HeroSection />
      
      {/* Magic Bento Grid */}
      <section className="relative py-20">
        <MagicBento />
      </section>
      
      {/* Process Timeline */}
      <section className="relative py-20">
        <ProcessTimeline />
      </section>
      
      {/* Footer */}
      <footer className="relative py-12 border-t border-white/10">
        <div className="max-w-6xl mx-auto px-4 text-center">
          <p className="text-gray-400">
            © 2024 智能投放系统. 专业的保险数字化营销平台
          </p>
        </div>
      </footer>
    </main>
  )
}
