import type { Metadata } from 'next'
import { Inter } from 'next/font/google'
import './globals.css'

const inter = Inter({ subsets: ['latin'] })

export const metadata: Metadata = {
  title: '智能投放系统 - 保险数字化营销平台',
  description: '专业的保险流量投放解决方案，数据驱动精准营销，助力保险行业数字化转型。',
  keywords: '保险投放,智能营销,数据分析,流量聚合,精准匹配',
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="zh-CN">
      <body className={inter.className}>
        <div className="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900">
          {children}
        </div>
      </body>
    </html>
  )
}
